import { Injectable } from '@nestjs/common';
import { TodoService } from '@/modules/todolists/services/todo.service';
import { EmployeeService } from '@/modules/hrm/employees/services/employee.service';
import { StatisticsService } from '@/modules/todolists/services/statistics.service';
import { 
  ToolDefinition, 
  ToolCategory, 
  ToolContext, 
  ToolResult 
} from '../interfaces/tool.interface';

/**
 * Business Tools Provider cho ERP system
 * Cung cấp tất cả tools liên quan đến business logic
 * Mỗi tool tương ứng với một chức năng cụ thể trong ERP
 */
@Injectable()
export class BusinessToolsProvider {
  constructor(
    // Inject các service cần thiết để tools có thể hoạt động
    private readonly todoService: TodoService,
    private readonly employeeService: EmployeeService,
    private readonly statisticsService: StatisticsService,
  ) {}

  /**
   * L<PERSON>y tất cả business tools đã định nghĩa
   * Method này sẽ được gọi khi khởi tạo để register tools
   */
  getAllBusinessTools(): ToolDefinition[] {
    return [
      this.getTodoStatisticsTool(),      // Thống kê công việc
      this.getEmployeeInfoTool(),        // Thông tin nhân viên
      this.getLateEmployeesTool(),       // Nhân viên đi muộn
      this.getOverdueTasksTool(),        // Công việc quá hạn
      this.getUserTasksTool(),           // Công việc của user
      this.getTeamStatisticsTool(),      // Thống kê team
      this.getEmployeeListTool(),        // Danh sách nhân viên
      this.getTaskDetailsTool(),         // Chi tiết công việc
    ];
  }

  /**
   * Tool: Lấy thống kê công việc
   * Cung cấp thống kê tổng quan về công việc trong khoảng thời gian
   */
  private getTodoStatisticsTool(): ToolDefinition {
    return {
      name: 'get_todo_statistics',
      description: 'Lấy thống kê tổng quan về công việc (todo) của người dùng hoặc team trong khoảng thời gian nhất định',
      parameters: {
        type: 'object',
        properties: {
          userId: {
            type: 'number',
            description: 'ID người dùng cần lấy thống kê (optional, mặc định là người dùng hiện tại)',
          },
          teamId: {
            type: 'number',
            description: 'ID team cần lấy thống kê (optional)',
          },
          dateRange: {
            type: 'string',
            description: 'Khoảng thời gian thống kê',
            enum: ['today', 'week', 'month', 'quarter', 'year'],
            default: 'week',
          },
          includeCompleted: {
            type: 'boolean',
            description: 'Có bao gồm công việc đã hoàn thành không',
            default: true,
          },
        },
        required: [], // Không có tham số bắt buộc
      },
      // Handler function - logic chính của tool
      handler: async (params: any, context: ToolContext): Promise<ToolResult> => {
        try {
          // Lấy userId từ params hoặc context (user hiện tại)
          const userId = params.userId || context.userId;
          const dateRange = params.dateRange || 'week';

          // Gọi service để lấy thống kê
          const stats = await this.statisticsService.getTodoStatistics(
            context.tenantId, // Đảm bảo tenant isolation
            userId,
            {
              dateRange,
              teamId: params.teamId,
              includeCompleted: params.includeCompleted ?? true,
            }
          );

          // Trả về kết quả thành công
          return {
            success: true,
            data: stats,
            message: `Thống kê công việc ${dateRange} đã được lấy thành công`,
            metadata: {
              userId,
              dateRange,
              teamId: params.teamId,
            },
          };
        } catch (error) {
          // Xử lý lỗi và trả về error response
          return {
            success: false,
            error: error.message,
            message: 'Không thể lấy thống kê công việc',
          };
        }
      },
      category: ToolCategory.BUSINESS,
      tenantIsolated: true, // Cần tenant isolation
      rateLimit: 30, // Tối đa 30 calls/minute
      cacheable: true, // Có thể cache kết quả
      cacheTtl: 300, // Cache 5 phút
    };
  }

  /**
   * Tool: Lấy thông tin nhân viên
   */
  private getEmployeeInfoTool(): ToolDefinition {
    return {
      name: 'get_employee_info',
      description: 'Lấy thông tin chi tiết của nhân viên theo ID hoặc tên',
      parameters: {
        type: 'object',
        properties: {
          employeeId: {
            type: 'number',
            description: 'ID nhân viên',
          },
          employeeName: {
            type: 'string',
            description: 'Tên nhân viên (tìm kiếm gần đúng)',
          },
          includeStats: {
            type: 'boolean',
            description: 'Có bao gồm thống kê hiệu suất không',
            default: false,
          },
        },
        required: [],
      },
      handler: async (params: any, context: ToolContext): Promise<ToolResult> => {
        try {
          let employee;

          if (params.employeeId) {
            employee = await this.employeeService.findById(context.tenantId, params.employeeId);
          } else if (params.employeeName) {
            const employees = await this.employeeService.searchByName(
              context.tenantId, 
              params.employeeName
            );
            employee = employees[0]; // Lấy kết quả đầu tiên
          } else {
            // Lấy thông tin nhân viên hiện tại
            employee = await this.employeeService.findByUserId(context.tenantId, context.userId);
          }

          if (!employee) {
            return {
              success: false,
              message: 'Không tìm thấy nhân viên',
            };
          }

          let stats = null;
          if (params.includeStats) {
            stats = await this.statisticsService.getEmployeeStats(
              context.tenantId,
              employee.id
            );
          }

          return {
            success: true,
            data: {
              employee,
              stats,
            },
            message: `Thông tin nhân viên ${employee.fullName} đã được lấy thành công`,
            metadata: {
              employeeId: employee.id,
              includeStats: params.includeStats,
            },
          };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            message: 'Không thể lấy thông tin nhân viên',
          };
        }
      },
      category: ToolCategory.BUSINESS,
      tenantIsolated: true,
      rateLimit: 60,
      cacheable: true,
      cacheTtl: 600, // 10 minutes
    };
  }

  /**
   * Tool: Lấy danh sách nhân viên đi muộn
   */
  private getLateEmployeesTool(): ToolDefinition {
    return {
      name: 'get_late_employees',
      description: 'Lấy danh sách nhân viên đi muộn trong ngày hoặc khoảng thời gian nhất định',
      parameters: {
        type: 'object',
        properties: {
          date: {
            type: 'string',
            description: 'Ngày cần kiểm tra (YYYY-MM-DD), mặc định là hôm nay',
          },
          departmentId: {
            type: 'number',
            description: 'ID phòng ban (optional)',
          },
          minLateMinutes: {
            type: 'number',
            description: 'Số phút đi muộn tối thiểu để tính',
            default: 1,
          },
        },
        required: [],
      },
      handler: async (params: any, context: ToolContext): Promise<ToolResult> => {
        try {
          const date = params.date || new Date().toISOString().split('T')[0];
          
          const lateEmployees = await this.employeeService.getLateEmployees(
            context.tenantId,
            {
              date,
              departmentId: params.departmentId,
              minLateMinutes: params.minLateMinutes || 1,
            }
          );

          return {
            success: true,
            data: lateEmployees,
            message: `Tìm thấy ${lateEmployees.length} nhân viên đi muộn ngày ${date}`,
            metadata: {
              date,
              departmentId: params.departmentId,
              count: lateEmployees.length,
            },
          };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            message: 'Không thể lấy danh sách nhân viên đi muộn',
          };
        }
      },
      category: ToolCategory.BUSINESS,
      tenantIsolated: true,
      rateLimit: 30,
      cacheable: true,
      cacheTtl: 300, // 5 minutes
    };
  }

  /**
   * Tool: Lấy công việc quá hạn
   */
  private getOverdueTasksTool(): ToolDefinition {
    return {
      name: 'get_overdue_tasks',
      description: 'Lấy danh sách công việc quá hạn của người dùng hoặc team',
      parameters: {
        type: 'object',
        properties: {
          userId: {
            type: 'number',
            description: 'ID người dùng (optional, mặc định là người dùng hiện tại)',
          },
          teamId: {
            type: 'number',
            description: 'ID team (optional)',
          },
          priority: {
            type: 'string',
            description: 'Mức độ ưu tiên',
            enum: ['low', 'medium', 'high', 'urgent'],
          },
          limit: {
            type: 'number',
            description: 'Số lượng tối đa kết quả trả về',
            default: 20,
            maximum: 100,
          },
        },
        required: [],
      },
      handler: async (params: any, context: ToolContext): Promise<ToolResult> => {
        try {
          const userId = params.userId || context.userId;
          
          const overdueTasks = await this.todoService.getOverdueTasks(
            context.tenantId,
            {
              userId,
              teamId: params.teamId,
              priority: params.priority,
              limit: params.limit || 20,
            }
          );

          return {
            success: true,
            data: overdueTasks,
            message: `Tìm thấy ${overdueTasks.length} công việc quá hạn`,
            metadata: {
              userId,
              teamId: params.teamId,
              count: overdueTasks.length,
            },
            suggestions: overdueTasks.length > 0 ? [
              {
                title: 'Xem chi tiết công việc đầu tiên',
                action: 'get_task_details',
                parameters: { taskId: overdueTasks[0].id },
              },
            ] : [],
          };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            message: 'Không thể lấy danh sách công việc quá hạn',
          };
        }
      },
      category: ToolCategory.BUSINESS,
      tenantIsolated: true,
      rateLimit: 30,
      cacheable: true,
      cacheTtl: 180, // 3 minutes
    };
  }

  /**
   * Tool: Lấy công việc của người dùng
   */
  private getUserTasksTool(): ToolDefinition {
    return {
      name: 'get_user_tasks',
      description: 'Lấy danh sách công việc của người dùng theo trạng thái',
      parameters: {
        type: 'object',
        properties: {
          userId: {
            type: 'number',
            description: 'ID người dùng (optional, mặc định là người dùng hiện tại)',
          },
          status: {
            type: 'string',
            description: 'Trạng thái công việc',
            enum: ['pending', 'in_progress', 'completed', 'cancelled'],
            default: 'pending',
          },
          priority: {
            type: 'string',
            description: 'Mức độ ưu tiên',
            enum: ['low', 'medium', 'high', 'urgent'],
          },
          dueDate: {
            type: 'string',
            description: 'Ngày deadline (YYYY-MM-DD)',
          },
          limit: {
            type: 'number',
            description: 'Số lượng tối đa kết quả',
            default: 10,
            maximum: 50,
          },
        },
        required: [],
      },
      handler: async (params: any, context: ToolContext): Promise<ToolResult> => {
        try {
          const userId = params.userId || context.userId;
          
          const tasks = await this.todoService.getUserTasks(
            context.tenantId,
            userId,
            {
              status: params.status || 'pending',
              priority: params.priority,
              dueDate: params.dueDate,
              limit: params.limit || 10,
            }
          );

          return {
            success: true,
            data: tasks,
            message: `Tìm thấy ${tasks.length} công việc`,
            metadata: {
              userId,
              status: params.status,
              count: tasks.length,
            },
          };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            message: 'Không thể lấy danh sách công việc',
          };
        }
      },
      category: ToolCategory.BUSINESS,
      tenantIsolated: true,
      rateLimit: 60,
      cacheable: true,
      cacheTtl: 120, // 2 minutes
    };
  }

  /**
   * Tool: Lấy thống kê team
   */
  private getTeamStatisticsTool(): ToolDefinition {
    return {
      name: 'get_team_statistics',
      description: 'Lấy thống kê hiệu suất của team/phòng ban',
      parameters: {
        type: 'object',
        properties: {
          teamId: {
            type: 'number',
            description: 'ID team/phòng ban',
          },
          dateRange: {
            type: 'string',
            description: 'Khoảng thời gian thống kê',
            enum: ['week', 'month', 'quarter'],
            default: 'month',
          },
          includeIndividual: {
            type: 'boolean',
            description: 'Có bao gồm thống kê từng cá nhân không',
            default: false,
          },
        },
        required: ['teamId'],
      },
      handler: async (params: any, context: ToolContext): Promise<ToolResult> => {
        try {
          const teamStats = await this.statisticsService.getTeamStatistics(
            context.tenantId,
            params.teamId,
            {
              dateRange: params.dateRange || 'month',
              includeIndividual: params.includeIndividual || false,
            }
          );

          return {
            success: true,
            data: teamStats,
            message: `Thống kê team đã được lấy thành công`,
            metadata: {
              teamId: params.teamId,
              dateRange: params.dateRange,
            },
          };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            message: 'Không thể lấy thống kê team',
          };
        }
      },
      category: ToolCategory.BUSINESS,
      tenantIsolated: true,
      rateLimit: 20,
      cacheable: true,
      cacheTtl: 600, // 10 minutes
    };
  }

  /**
   * Tool: Lấy danh sách nhân viên
   */
  private getEmployeeListTool(): ToolDefinition {
    return {
      name: 'get_employee_list',
      description: 'Lấy danh sách nhân viên theo bộ lọc',
      parameters: {
        type: 'object',
        properties: {
          departmentId: {
            type: 'number',
            description: 'ID phòng ban',
          },
          position: {
            type: 'string',
            description: 'Chức vụ',
          },
          status: {
            type: 'string',
            description: 'Trạng thái nhân viên',
            enum: ['active', 'inactive', 'on_leave'],
            default: 'active',
          },
          limit: {
            type: 'number',
            description: 'Số lượng tối đa kết quả',
            default: 20,
            maximum: 100,
          },
        },
        required: [],
      },
      handler: async (params: any, context: ToolContext): Promise<ToolResult> => {
        try {
          const employees = await this.employeeService.getEmployeeList(
            context.tenantId,
            {
              departmentId: params.departmentId,
              position: params.position,
              status: params.status || 'active',
              limit: params.limit || 20,
            }
          );

          return {
            success: true,
            data: employees,
            message: `Tìm thấy ${employees.length} nhân viên`,
            metadata: {
              count: employees.length,
              departmentId: params.departmentId,
            },
          };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            message: 'Không thể lấy danh sách nhân viên',
          };
        }
      },
      category: ToolCategory.BUSINESS,
      tenantIsolated: true,
      rateLimit: 30,
      cacheable: true,
      cacheTtl: 300, // 5 minutes
    };
  }

  /**
   * Tool: Lấy chi tiết công việc
   */
  private getTaskDetailsTool(): ToolDefinition {
    return {
      name: 'get_task_details',
      description: 'Lấy thông tin chi tiết của một công việc',
      parameters: {
        type: 'object',
        properties: {
          taskId: {
            type: 'number',
            description: 'ID công việc',
          },
          includeComments: {
            type: 'boolean',
            description: 'Có bao gồm comments không',
            default: true,
          },
          includeHistory: {
            type: 'boolean',
            description: 'Có bao gồm lịch sử thay đổi không',
            default: false,
          },
        },
        required: ['taskId'],
      },
      handler: async (params: any, context: ToolContext): Promise<ToolResult> => {
        try {
          const taskDetails = await this.todoService.getTaskDetails(
            context.tenantId,
            params.taskId,
            {
              includeComments: params.includeComments ?? true,
              includeHistory: params.includeHistory ?? false,
            }
          );

          if (!taskDetails) {
            return {
              success: false,
              message: 'Không tìm thấy công việc',
            };
          }

          return {
            success: true,
            data: taskDetails,
            message: `Chi tiết công việc "${taskDetails.title}" đã được lấy thành công`,
            metadata: {
              taskId: params.taskId,
              includeComments: params.includeComments,
              includeHistory: params.includeHistory,
            },
          };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            message: 'Không thể lấy chi tiết công việc',
          };
        }
      },
      category: ToolCategory.BUSINESS,
      tenantIsolated: true,
      rateLimit: 60,
      cacheable: true,
      cacheTtl: 180, // 3 minutes
    };
  }
}
